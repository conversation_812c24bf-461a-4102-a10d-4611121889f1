{"lastValidatedTimestamp": 1754103239184, "projects": {"C:\\Users\\<USER>\\shop-bze\\my-app": {"name": "volo-app-template", "version": "0.3.0"}, "C:\\Users\\<USER>\\shop-bze\\my-app\\database-server": {"name": "database-server", "version": "0.1.0"}, "C:\\Users\\<USER>\\shop-bze\\my-app\\server": {"name": "server", "version": "0.1.0"}, "C:\\Users\\<USER>\\shop-bze\\my-app\\ui": {"name": "ui", "version": "0.0.0"}}, "pnpmfileExists": false, "settings": {"autoInstallPeers": true, "catalogs": {}, "dedupeDirectDeps": false, "dedupeInjectedDeps": true, "dedupePeerDependents": true, "dev": true, "excludeLinksFromLockfile": false, "hoistPattern": ["*"], "hoistWorkspacePackages": true, "injectWorkspacePackages": false, "linkWorkspacePackages": false, "nodeLinker": "isolated", "optional": true, "preferWorkspacePackages": false, "production": true, "publicHoistPattern": [], "workspacePackagePatterns": ["server", "database-server", "ui"]}, "filteredInstall": true}